@layer base {
  /* Improved default focus styles */
  :focus-visible {
    @apply outline-none ring-2 ring-green-500 ring-offset-2;
  }

  /* Better text rendering */
  body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  /* Improved default scrollbar */
  ::-webkit-scrollbar {
    @apply w-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full hover:bg-gray-400 transition-colors;
  }

  /* Improved text selection */
  ::selection {
    @apply bg-green-500/20 text-green-900;
  }

  /* Better form element defaults */
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="number"],
  input[type="tel"],
  input[type="url"],
  input[type="search"],
  textarea {
    @apply appearance-none rounded-md shadow-sm border-gray-300 focus:border-green-500 focus:ring focus:ring-green-500 focus:ring-opacity-50;
  }

  /* Improved button defaults */
  button {
    @apply focus:outline-none focus-visible:ring-2 focus-visible:ring-green-500 focus-visible:ring-offset-2;
  }

  /* Better link defaults */
  a {
    @apply focus:outline-none focus-visible:ring-2 focus-visible:ring-green-500 focus-visible:ring-offset-2;
  }

  /* Improved heading defaults */
  h1, h2, h3, h4, h5, h6 {
    @apply tracking-tight text-gray-900;
  }

  /* Better list defaults */
  ul, ol {
    @apply list-none;
  }

  /* Print styles */
  @media print {
    .no-print {
      display: none !important;
    }
    
    a {
      text-decoration: none !important;
    }
    
    body {
      @apply text-black bg-white;
    }
  }
}