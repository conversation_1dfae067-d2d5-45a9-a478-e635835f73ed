# Contract PDF Design System

## 🎯 **Overview**

This design system provides **effortless, section-by-section customization** for the Arbeidskontrakt PDF generator. All styling is centralized in a single file, eliminating configuration drift and cross-file hunting.

## 📁 **File Structure**

```
src/lib/meta/design/
├── contract-design-system.ts  # Single source of truth for ALL styling
└── README.md                  # This documentation
```

## 🎨 **Design Philosophy**

- **Section-based isolation**: Each design group is independent
- **Zero configuration drift**: Single source of truth
- **Incremental customization**: Modify one section without affecting others
- **Maintainability**: Clear structure, no cross-file hunting
- **No codebase bloat**: Clean, organized, efficient

## 🏗️ **Architecture**

### **1. Design Tokens (Global Variables)**
```typescript
designTokens = {
  colors: { primary: '#059669', ... },
  typography: { sizes: { xs: 9, sm: 11, ... } },
  spacing: { xs: 2, sm: 5, ... },
  layout: { pageMargin: 30, ... }
}
```

### **2. Section-Based Design Groups**
- **`pageDesign`** - Overall page layout
- **`headerDesign`** - Company branding and title
- **`contentDesign`** - Main contract sections
- **`legalDesign`** - Legal text and disclaimers
- **`signatureDesign`** - Signature areas

## 🛠️ **How to Customize**

### **Quick Color Changes**
```typescript
// In contract-design-system.ts
colors: {
  primary: '#059669',    // Change to your brand color
  secondary: '#374151',  // Dark gray for titles
  accent: '#d1d5db',     // Light gray for borders
}
```

### **Typography Adjustments**
```typescript
typography: {
  sizes: {
    xs: 9,    // Legal text
    sm: 11,   // Body text
    md: 13,   // Section titles
    lg: 16,   // Main title
    xl: 18,   // Company name
  }
}
```

### **Spacing Modifications**
```typescript
spacing: {
  xs: 2,    // Tiny gaps
  sm: 5,    // Small gaps
  md: 10,   // Medium gaps
  lg: 15,   // Large gaps
  xl: 20,   // Extra large gaps
  xxl: 30,  // Page margins
  xxxl: 40, // Major sections
}
```

## 📋 **Section-by-Section Customization**

### **Header Section**
```typescript
headerDesign = {
  container: { /* Header container styling */ },
  companyName: { /* Company name styling */ },
  companyInfo: { /* Company info styling */ },
  title: { /* Contract title styling */ }
}
```

**What it controls:**
- Company name size, color, weight
- Company info (org number, address) styling
- Contract title appearance
- Header borders and spacing

### **Content Sections**
```typescript
contentDesign = {
  section: { /* Section container */ },
  sectionTitle: { /* Section headers */ },
  row: { /* Two-column layouts */ },
  column: { /* Column content */ },
  label: { /* Field labels */ },
  text: { /* Body text */ },
  keepTogether: { /* Page break controls */ }
}
```

**What it controls:**
- Section spacing and margins
- Section title colors and borders
- Two-column vs single-column layouts
- Label and text styling
- Page break behavior

### **Signature Section**
```typescript
signatureDesign = {
  container: { /* Signature area container */ },
  box: { /* Individual signature boxes */ },
  line: { /* Signature lines */ },
  label: { /* Signature labels */ },
  text: { /* Signature text */ }
}
```

**What it controls:**
- Signature area positioning
- Signature line appearance
- Date and name styling
- Box spacing and alignment

### **Footer Section**
```typescript
footerDesign = {
  pageNumber: { /* Page numbering styling and positioning */ }
}
```

**What it controls:**
- Page number positioning (lower right corner)
- Page number font size and color
- Absolute positioning from bottom and right edges

## 🚀 **Usage Examples**

### **Example 1: Change Brand Color**
```typescript
// In contract-design-system.ts
colors: {
  primary: '#1e40af',  // Change from green to blue
  // ... rest unchanged
}
```
**Result:** All section titles, company name, and borders become blue.

### **Example 2: Increase Section Spacing**
```typescript
// In contract-design-system.ts
spacing: {
  // ... existing values
  lg: 20,   // Increase from 15 to 20
  xl: 25,   // Increase from 20 to 25
}
```
**Result:** More breathing room between sections.

### **Example 3: Larger Company Name**
```typescript
// In contract-design-system.ts
headerDesign = {
  companyName: {
    fontSize: designTokens.typography.sizes.xl + 4, // 22px instead of 18px
    // ... rest unchanged
  }
}
```
**Result:** More prominent company branding.

## 🎯 **Benefits**

### **Before (Old System)**
- ❌ Styles scattered across 80+ lines
- ❌ Hard-coded values everywhere
- ❌ Cross-file hunting for changes
- ❌ Risk of breaking other sections
- ❌ Inconsistent spacing/colors

### **After (New System)**
- ✅ Single file controls everything
- ✅ Logical section-based organization
- ✅ Design tokens prevent inconsistency
- ✅ Change one section without affecting others
- ✅ Clear documentation and examples

## 🔧 **Advanced Customization**

### **Adding New Design Tokens**
```typescript
designTokens = {
  // Add new token categories
  shadows: {
    light: '0 1px 3px rgba(0,0,0,0.1)',
    medium: '0 4px 6px rgba(0,0,0,0.1)',
  },
  borders: {
    radius: 4,
    style: 'solid',
  }
}
```

### **Creating New Section Groups**
```typescript
// Add new section for future features
export const footerDesign = {
  container: { /* Footer styling */ },
  text: { /* Footer text */ },
} as const;
```

## 📝 **Best Practices**

1. **Always use design tokens** instead of hard-coded values
2. **Test changes incrementally** - modify one section at a time
3. **Keep section isolation** - don't mix styles between sections
4. **Document custom changes** - add comments for future reference
5. **Use TypeScript** - leverage type safety for consistency

## 🎉 **Result**

**Effortless, incremental section design** with **zero configuration drift** and **absolute maintainability** - all through one importable config! 🚀
