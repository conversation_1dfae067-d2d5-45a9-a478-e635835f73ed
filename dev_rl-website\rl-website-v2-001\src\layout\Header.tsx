import { useState, useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import { Menu, X } from "lucide-react";
import { Logo } from "@/ui/Logo";
import { Container } from "@/ui/Container";

const Header = () => {
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const [isScrolled, setIsScrolled] = useState(false);
    const location = useLocation();

    const navigation = [
        { name: "Hjem", href: "/" },
        { name: "Hva vi gjør", href: "/hva" },
        { name: "Prosjekter", href: "/prosjekter" },
        { name: "Hvem er vi", href: "/hvem" },
    ];

    useEffect(() => {
        const handleScroll = () => {
            setIsScrolled(window.scrollY > 10);
        };

        window.addEventListener("scroll", handleScroll);
        return () => window.removeEventListener("scroll", handleScroll);
    }, []);

    // Close mobile menu when route changes
    useEffect(() => {
        setIsMenuOpen(false);
    }, [location.pathname]);

    return (
        <header
            className={`sticky top-0 z-50 w-full transition-all duration-200 ${
                isScrolled
                    ? "bg-white shadow-md"
                    : "bg-white/90 backdrop-blur-sm"
            }`}
        >
            <Container>
                <div className="flex h-16 items-center justify-between">
                    <Link
                        to="/"
                        className="flex items-center"
                        aria-label="Ringerike Landskap"
                    >
                        <Logo
                            variant="full"
                            className={
                                isScrolled ? "text-gray-900" : "text-gray-900"
                            }
                        />
                    </Link>

                    {/* Desktop navigation */}
                    <nav className="hidden md:flex md:items-center md:space-x-8">
                        {navigation.map((item) => (
                            <Link
                                key={item.name}
                                to={item.href}
                                className={`text-sm font-medium transition-colors hover:text-green-600 ${
                                    location.pathname === item.href
                                        ? "text-green-600"
                                        : "text-gray-700"
                                }`}
                            >
                                {item.name}
                            </Link>
                        ))}
                        <Link
                            to="/kontakt"
                            className="rounded-md bg-green-500 px-4 py-2 text-sm font-medium text-white hover:bg-green-600 transition-colors"
                        >
                            Kontakt
                        </Link>
                    </nav>

                    {/* Mobile menu button */}
                    <button
                        type="button"
                        className="md:hidden rounded-md p-2 text-gray-700"
                        onClick={() => setIsMenuOpen(!isMenuOpen)}
                        aria-expanded={isMenuOpen}
                        aria-controls="mobile-menu"
                    >
                        <span className="sr-only">
                            {isMenuOpen ? "Lukk meny" : "Åpne meny"}
                        </span>
                        {isMenuOpen ? (
                            <X className="h-6 w-6" />
                        ) : (
                            <Menu className="h-6 w-6" />
                        )}
                    </button>
                </div>
            </Container>

            {/* Mobile menu */}
            {isMenuOpen && (
                <div className="md:hidden" id="mobile-menu">
                    <div className="space-y-1 px-4 pb-3 pt-2">
                        {navigation.map((item) => (
                            <Link
                                key={item.name}
                                to={item.href}
                                className={`block rounded-md px-3 py-2 text-base font-medium ${
                                    location.pathname === item.href
                                        ? "bg-green-50 text-green-600"
                                        : "text-gray-700 hover:bg-gray-50 hover:text-gray-900"
                                }`}
                            >
                                {item.name}
                            </Link>
                        ))}
                        <Link
                            to="/kontakt"
                            className="block w-full rounded-md bg-green-500 px-3 py-2 text-center text-base font-medium text-white hover:bg-green-600 mt-4"
                        >
                            Kontakt
                        </Link>
                    </div>
                </div>
            )}
        </header>
    );
};

export default Header;
