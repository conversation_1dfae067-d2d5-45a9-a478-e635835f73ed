/**
 * Type definitions for contact information
 *
 * These types are designed with SEO considerations in mind:
 * - Structured to match schema.org requirements
 * - Support for location-specific SEO data targeting Ringerike region
 * - Customer review data structures with location mentions
 * - Business category classifications
 * - Core service definitions with location relevance
 * - Geographic targeting for 20-50km service radius
 */

/**
 * Business category for schema.org and industry-specific search
 */
export interface BusinessCategory {
  id: string;
  name: string;
  schemaType: string;
  googleCategory: string;
}

/**
 * Owner information for personal touch in SEO
 */
export interface OwnerInfo {
  name: string;
  expertise: string[];
  description: string;
}

/**
 * Company information with SEO attributes focused on local service area
 */
export interface CompanyInfo {
  name: string;
  orgNumber: string;
  vatNumber: string;
  foundedYear: number;
  categories: BusinessCategory[];
  priceRange: string;
  description: string;
  extendedDescription: string;
  yearEstablished: string;
  paymentAccepted: string[];
  languages: string[];
  owners: OwnerInfo[];
  serviceRadius: string;
  mainServiceArea: string;
  counties: string[];
  municipalities: string[];
}

/**
 * Team member information
 */
export interface TeamMember {
  id: string;
  name: string;
  title: string;
  phone: string;
  phoneRaw: string;
  email: string;
  image: string;
  isMainContact: boolean;
}

/**
 * Address information
 */
export interface Address {
  street: string;
  postalCode: string;
  city: string;
  municipality: string;
  county: string;
  country: string;
}

/**
 * Social media links
 */
export interface SocialMedia {
  facebook: string;
  instagram?: string;
  linkedin?: string;
  twitter?: string;
}

/**
 * Opening hours
 */
export interface OpeningHours {
  weekdays: string;
  weekend: string;
}

/**
 * Form defaults
 */
export interface FormDefaults {
  defaultSubject: string;
  defaultMessage: string;
  successMessage: string;
  errorMessage: string;
}

/**
 * Geographic shape for service area
 */
export interface GeoShape {
  type: string;
  coordinates: [number, number];
  radius: string;
}

/**
 * Service area with SEO-optimized descriptions for local targeting
 */
export interface ServiceArea {
  id: string;
  name: string;
  postalCodes: string[];
  radius: number;
  priority: number; // 1 = highest, 3 = lower
  description: string;
  primaryServices: string[];
  geoShape: GeoShape;
  landmarks: string[]; // Local landmarks for SEO content
  terrainFeatures: string[]; // Terrain characteristics for targeted content
}

/**
 * Customer review for schema.org with location mentions
 */
export interface CustomerReview {
  author: string;
  location: string; // Location mention for local SEO
  rating: number;
  datePublished: string;
  reviewBody: string;
  itemReviewed: {
    type: string;
    name: string;
  };
}

/**
 * Core service with SEO-optimized descriptions
 */
export interface CoreService {
  id: string;
  name: string;
  description: string;
  benefits: string[];
  popularIn: string[]; // Locations where this service is popular
}

/**
 * Complete contact information with SEO enhancements for local targeting
 */
export interface ContactInformation {
  phone: string;
  email: string;
  address: Address;
  social: SocialMedia;
  openingHours: OpeningHours;
  company: CompanyInfo;
  team: TeamMember[];
  form: FormDefaults;
  serviceAreas: ServiceArea[];
  reviews: CustomerReview[];
  coreServices: CoreService[];
}

/**
 * SEO metadata for location pages
 */
export interface LocationSeoMetadata {
  title: string;
  description: string;
  keywords: string[];
  schema: Record<string, any>;
}
