import { SITE_CONFIG } from "../constants/site";

interface SEOProps {
    title?: string;
    description?: string;
    image?: string;
    type?: "website" | "article";
    path?: string;
}

export const generateSEOMeta = ({
    title,
    description = SITE_CONFIG.description,
    image = SITE_CONFIG.ogImage,
    type = "website",
    path = "",
}: SEOProps) => {
    const fullTitle = title
        ? `${title} | ${SITE_CONFIG.name}`
        : SITE_CONFIG.name;
    const url = `${SITE_CONFIG.url}${path}`;

    return {
        title: fullTitle,
        meta: [
            {
                name: "description",
                content: description,
            },
            {
                property: "og:title",
                content: fullTitle,
            },
            {
                property: "og:description",
                content: description,
            },
            {
                property: "og:image",
                content: image,
            },
            {
                property: "og:url",
                content: url,
            },
            {
                property: "og:type",
                content: type,
            },
            {
                name: "twitter:card",
                content: "summary_large_image",
            },
            {
                name: "twitter:title",
                content: fullTitle,
            },
            {
                name: "twitter:description",
                content: description,
            },
            {
                name: "twitter:image",
                content: image,
            },
        ],
        link: [
            {
                rel: "canonical",
                href: url,
            },
        ],
    };
};

/**
 * SEO utilities for generating metadata
 */

/**
 * Generates metadata for SEO purposes
 */
export const generateMetadata = ({
    title,
    description,
    image,
    type = "website",
}: {
    title: string;
    description?: string;
    image?: string;
    type?: "website" | "article";
}) => {
    const baseTitle = "Ringerike Landskap";
    const baseDescription = "Din lokale anleggsgartner i Ringerike-regionen";
    const baseImage =
        "https://images.unsplash.com/photo-1558904541-efa843a96f01?auto=format&fit=crop&q=80";

    return {
        title: title ? `${title} | ${baseTitle}` : baseTitle,
        description: description || baseDescription,
        openGraph: {
            title: title ? `${title} | ${baseTitle}` : baseTitle,
            description: description || baseDescription,
            type,
            url: "https://ringerikelandskap.no",
            images: [
                {
                    url: image || baseImage,
                    width: 1200,
                    height: 630,
                    alt: title || baseTitle,
                },
            ],
            locale: "nb_NO",
            siteName: baseTitle,
        },
    };
};
