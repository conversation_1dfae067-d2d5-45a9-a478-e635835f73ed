/**
 * Debug utilities for development and testing
 * These utilities should be removed or disabled in production
 */

// Enable or disable debug logging
const DEBUG_ENABLED = process.env.NODE_ENV === 'development';

// Enable or disable seasonal debug logging
const SEASONAL_DEBUG_ENABLED = DEBUG_ENABLED;

/**
 * Log data access with source information
 * @param source The source of the data access (e.g., 'direct', 'api', 'sync')
 * @param dataType The type of data being accessed (e.g., 'services', 'projects')
 * @param details Additional details about the access
 */
export const logDataAccess = (
  source: 'direct' | 'api' | 'sync',
  dataType: 'services' | 'projects' | 'testimonials' | string,
  details?: any
): void => {
  if (!DEBUG_ENABLED) return;

  const timestamp = new Date().toISOString();
  const message = `[${timestamp}] Data Access: ${source} → ${dataType}`;

  console.group(message);
  console.log(`Source: ${source}`);
  console.log(`Data Type: ${dataType}`);
  if (details) {
    console.log('Details:', details);
  }
  console.groupEnd();
};

/**
 * Log API operation with timing information
 * @param operation The API operation being performed
 * @param startTime The start time of the operation
 * @param result The result of the operation (optional)
 */
export const logApiOperation = (
  operation: string,
  startTime: number,
  result?: any
): void => {
  if (!DEBUG_ENABLED) return;

  const endTime = performance.now();
  const duration = endTime - startTime;

  console.group(`[API] ${operation} (${duration.toFixed(2)}ms)`);
  console.log(`Operation: ${operation}`);
  console.log(`Duration: ${duration.toFixed(2)}ms`);
  if (result) {
    console.log('Result:', typeof result === 'object' ?
      `${Array.isArray(result) ? result.length + ' items' : 'Object'}` :
      result);
  }
  console.groupEnd();
};

/**
 * Create a data access tracker that wraps an existing function
 * @param fn The function to wrap
 * @param source The source of the data access
 * @param dataType The type of data being accessed
 * @returns A wrapped function that logs data access
 */
export function trackDataAccess<T, Args extends any[]>(
  fn: (...args: Args) => T,
  source: 'direct' | 'api' | 'sync',
  dataType: 'services' | 'projects' | 'testimonials' | string
): (...args: Args) => T {
  return (...args: Args): T => {
    logDataAccess(source, dataType, { args });
    const startTime = performance.now();
    const result = fn(...args);

    if (result instanceof Promise) {
      return result.then(resolvedResult => {
        logApiOperation(`${source}:${dataType}`, startTime, resolvedResult);
        return resolvedResult;
      }) as unknown as T;
    } else {
      logApiOperation(`${source}:${dataType}`, startTime, result);
      return result;
    }
  };
}

/**
 * Log seasonal data access for debugging
 * @param component The component accessing seasonal data
 * @param season The season being accessed
 * @param details Additional details about the access
 */
export const logSeasonalAccess = (
  component: string,
  season: string,
  details?: Record<string, any>
): void => {
  if (!SEASONAL_DEBUG_ENABLED) return;

  const timestamp = new Date().toISOString();
  const message = `[${timestamp}] Seasonal Access: ${component} → ${season}`;

  console.group(message);
  console.log(`Component: ${component}`);
  console.log(`Season: ${season}`);
  if (details) {
    console.log('Details:', details);
  }
  console.groupEnd();
};

/**
 * Add a visual indicator to the UI for debugging
 * @param text The text to display in the indicator
 * @param type The type of indicator (info, warning, error)
 */
export const addDebugIndicator = (
  text: string,
  type: 'info' | 'warning' | 'error' = 'info'
): void => {
  if (!DEBUG_ENABLED) return;

  // Remove any existing indicators
  const existingIndicator = document.getElementById('debug-indicator');
  if (existingIndicator) {
    existingIndicator.remove();
  }

  // Create a new indicator
  const indicator = document.createElement('div');
  indicator.id = 'debug-indicator';
  indicator.style.position = 'fixed';
  indicator.style.bottom = '10px';
  indicator.style.right = '10px';
  indicator.style.padding = '5px 10px';
  indicator.style.borderRadius = '4px';
  indicator.style.fontSize = '12px';
  indicator.style.fontWeight = 'bold';
  indicator.style.zIndex = '9999';

  // Set the indicator style based on the type
  switch (type) {
    case 'info':
      indicator.style.backgroundColor = '#0284c7';
      break;
    case 'warning':
      indicator.style.backgroundColor = '#d97706';
      break;
    case 'error':
      indicator.style.backgroundColor = '#dc2626';
      break;
  }

  indicator.style.color = 'white';
  indicator.textContent = text;

  // Add the indicator to the document
  document.body.appendChild(indicator);

  // Remove the indicator after 5 seconds
  setTimeout(() => {
    indicator.remove();
  }, 5000);
};
