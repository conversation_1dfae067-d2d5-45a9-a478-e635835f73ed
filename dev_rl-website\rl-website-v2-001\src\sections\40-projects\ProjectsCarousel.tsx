import React, { useState, useEffect, useCallback } from 'react';
import { ArrowLeft, ArrowRight } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import ProjectCard from './ProjectCard';
import { ProjectType } from '@/lib/types';

interface ProjectsCarouselProps {
  projects: ProjectType[];
  className?: string;
}

const ProjectsCarousel: React.FC<ProjectsCarouselProps> = ({
  projects,
  className
}) => {
  const navigate = useNavigate();
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const [touchStart, setTouchStart] = useState<number | null>(null);

  const handleProjectClick = useCallback((id: string) => {
    navigate(`/prosjekter/${id}`);
  }, [navigate]);

  const handlePrevious = useCallback(() => {
    if (isAnimating || !projects.length) return;
    setIsAnimating(true);
    setCurrentIndex((prev) => (prev === 0 ? projects.length - 1 : prev - 1));
    setTimeout(() => setIsAnimating(false), 500);
  }, [isAnimating, projects.length]);

  const handleNext = useCallback(() => {
    if (isAnimating || !projects.length) return;
    setIsAnimating(true);
    setCurrentIndex((prev) => (prev === projects.length - 1 ? 0 : prev + 1));
    setTimeout(() => setIsAnimating(false), 500);
  }, [isAnimating, projects.length]);

  useEffect(() => {
    const interval = setInterval(handleNext, 5000);
    return () => clearInterval(interval);
  }, [handleNext]);

  // Touch handlers for mobile swipe
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStart(e.touches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!touchStart) return;

    const touchEnd = e.touches[0].clientX;
    const diff = touchStart - touchEnd;

    if (Math.abs(diff) > 50) {
      if (diff > 0) {
        handleNext();
      } else {
        handlePrevious();
      }
      setTouchStart(null);
    }
  };

  if (!projects.length) {
    return null;
  }

  return (
    <section className={className}>
      <div
        className="relative group"
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
      >
        <div className="overflow-hidden rounded-lg">
          <div
            className="relative transition-transform duration-500 ease-in-out"
            style={{
              transform: `translateX(-${currentIndex * 100}%)`,
              display: 'flex'
            }}
          >
            {projects.map((project) => (
              <div
                key={project.id}
                className="w-full flex-shrink-0"
              >
                <div className="h-[350px] md:h-[400px] lg:h-[450px]">
                  <ProjectCard
                    project={project}
                    onProjectClick={handleProjectClick}
                    showTestimonial={true}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>

        <button
          onClick={handlePrevious}
          className="absolute -left-6 top-1/2 -translate-y-1/2 p-3 rounded-full bg-white/90 shadow-lg text-gray-700 hover:bg-white hover:text-gray-900 transition-all duration-300 opacity-0 group-hover:opacity-100 group-hover:-left-8 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
          aria-label="Forrige prosjekt"
        >
          <ArrowLeft className="w-5 h-5" />
        </button>

        <button
          onClick={handleNext}
          className="absolute -right-6 top-1/2 -translate-y-1/2 p-3 rounded-full bg-white/90 shadow-lg text-gray-700 hover:bg-white hover:text-gray-900 transition-all duration-300 opacity-0 group-hover:opacity-100 group-hover:-right-8 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
          aria-label="Neste prosjekt"
        >
          <ArrowRight className="w-5 h-5" />
        </button>

        <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-2">
          {projects.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentIndex(index)}
              className={`w-2 h-2 rounded-full transition-all duration-300 ${
                index === currentIndex
                  ? 'bg-white w-4'
                  : 'bg-white/50 hover:bg-white/70'
              }`}
              aria-label={`Gå til prosjekt ${index + 1}`}
              aria-current={index === currentIndex}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default ProjectsCarousel;