/**
 * Seasonal Module Verification Script
 * 
 * This script verifies that the seasonal module is working correctly
 * by checking that the current season is determined correctly and
 * that seasonal data is available for all seasons.
 * 
 * Usage: node scripts/verify-seasonal.js
 */

// Import required modules
const fs = require('fs');
const path = require('path');

// Define the paths to the seasonal files
const SEASONAL_CONSTANTS_PATH = path.join(__dirname, '..', 'src', 'lib', 'constants', 'seasonal.ts');
const SEASONAL_UTILS_PATH = path.join(__dirname, '..', 'src', 'lib', 'utils', 'seasonal.ts');
const SEASONAL_HOOK_PATH = path.join(__dirname, '..', 'src', 'lib', 'hooks', 'useSeasonalData.ts');

// Define the expected seasons
const EXPECTED_SEASONS = ['vår', 'sommer', 'høst', 'vinter'];

// Define the expected English seasons
const EXPECTED_ENGLISH_SEASONS = ['spring', 'summer', 'fall', 'winter'];

// Define the expected display names
const EXPECTED_DISPLAY_NAMES = ['Våren', 'Sommeren', 'Høsten', 'Vinteren'];

// Define the expected month to season mapping
const EXPECTED_MONTH_TO_SEASON = {
  0: 'vinter', // January
  1: 'vinter', // February
  2: 'vår',    // March
  3: 'vår',    // April
  4: 'vår',    // May
  5: 'sommer', // June
  6: 'sommer', // July
  7: 'sommer', // August
  8: 'høst',   // September
  9: 'høst',   // October
  10: 'høst',  // November
  11: 'vinter' // December
};

// Define the current season based on the current month
const getCurrentSeason = () => {
  const month = new Date().getMonth();
  return EXPECTED_MONTH_TO_SEASON[month];
};

// Verify that the seasonal constants file exists
console.log('Verifying seasonal constants file...');
if (fs.existsSync(SEASONAL_CONSTANTS_PATH)) {
  console.log('✅ Seasonal constants file exists');
  
  // Read the file content
  const content = fs.readFileSync(SEASONAL_CONSTANTS_PATH, 'utf8');
  
  // Check that all expected seasons are defined
  const allSeasonsFound = EXPECTED_SEASONS.every(season => content.includes(`'${season}'`));
  console.log(allSeasonsFound ? '✅ All seasons are defined' : '❌ Not all seasons are defined');
  
  // Check that all expected English seasons are defined
  const allEnglishSeasonsFound = EXPECTED_ENGLISH_SEASONS.every(season => content.includes(`'${season}'`));
  console.log(allEnglishSeasonsFound ? '✅ All English seasons are defined' : '❌ Not all English seasons are defined');
  
  // Check that all expected display names are defined
  const allDisplayNamesFound = EXPECTED_DISPLAY_NAMES.every(name => content.includes(`'${name}'`));
  console.log(allDisplayNamesFound ? '✅ All display names are defined' : '❌ Not all display names are defined');
  
  // Check that the month to season mapping is defined
  const monthToSeasonFound = content.includes('MONTH_TO_SEASON');
  console.log(monthToSeasonFound ? '✅ Month to season mapping is defined' : '❌ Month to season mapping is not defined');
  
  // Check that seasonal services are defined
  const seasonalServicesFound = content.includes('SEASONAL_SERVICES');
  console.log(seasonalServicesFound ? '✅ Seasonal services are defined' : '❌ Seasonal services are not defined');
  
  // Check that seasonal projects are defined
  const seasonalProjectsFound = content.includes('SEASONAL_PROJECTS');
  console.log(seasonalProjectsFound ? '✅ Seasonal projects are defined' : '❌ Seasonal projects are not defined');
} else {
  console.log('❌ Seasonal constants file does not exist');
}

// Verify that the seasonal utils file exists
console.log('\nVerifying seasonal utils file...');
if (fs.existsSync(SEASONAL_UTILS_PATH)) {
  console.log('✅ Seasonal utils file exists');
  
  // Read the file content
  const content = fs.readFileSync(SEASONAL_UTILS_PATH, 'utf8');
  
  // Check that getCurrentSeason is defined
  const getCurrentSeasonFound = content.includes('getCurrentSeason');
  console.log(getCurrentSeasonFound ? '✅ getCurrentSeason is defined' : '❌ getCurrentSeason is not defined');
  
  // Check that getSeasonDisplayName is defined
  const getSeasonDisplayNameFound = content.includes('getSeasonDisplayName');
  console.log(getSeasonDisplayNameFound ? '✅ getSeasonDisplayName is defined' : '❌ getSeasonDisplayName is not defined');
  
  // Check that getEnglishSeasonName is defined
  const getEnglishSeasonNameFound = content.includes('getEnglishSeasonName');
  console.log(getEnglishSeasonNameFound ? '✅ getEnglishSeasonName is defined' : '❌ getEnglishSeasonName is not defined');
  
  // Check that serviceMatchesSeason is defined
  const serviceMatchesSeasonFound = content.includes('serviceMatchesSeason');
  console.log(serviceMatchesSeasonFound ? '✅ serviceMatchesSeason is defined' : '❌ serviceMatchesSeason is not defined');
  
  // Check that projectMatchesSeason is defined
  const projectMatchesSeasonFound = content.includes('projectMatchesSeason');
  console.log(projectMatchesSeasonFound ? '✅ projectMatchesSeason is defined' : '❌ projectMatchesSeason is not defined');
} else {
  console.log('❌ Seasonal utils file does not exist');
}

// Verify that the seasonal hook file exists
console.log('\nVerifying seasonal hook file...');
if (fs.existsSync(SEASONAL_HOOK_PATH)) {
  console.log('✅ Seasonal hook file exists');
  
  // Read the file content
  const content = fs.readFileSync(SEASONAL_HOOK_PATH, 'utf8');
  
  // Check that useSeasonalData is defined
  const useSeasonalDataFound = content.includes('useSeasonalData');
  console.log(useSeasonalDataFound ? '✅ useSeasonalData is defined' : '❌ useSeasonalData is not defined');
  
  // Check that the hook returns the current season
  const currentSeasonFound = content.includes('currentSeason');
  console.log(currentSeasonFound ? '✅ Hook returns currentSeason' : '❌ Hook does not return currentSeason');
  
  // Check that the hook returns the current season in English
  const currentSeasonEnglishFound = content.includes('currentSeasonEnglish');
  console.log(currentSeasonEnglishFound ? '✅ Hook returns currentSeasonEnglish' : '❌ Hook does not return currentSeasonEnglish');
  
  // Check that the hook returns the current season display name
  const currentSeasonDisplayFound = content.includes('currentSeasonDisplay');
  console.log(currentSeasonDisplayFound ? '✅ Hook returns currentSeasonDisplay' : '❌ Hook does not return currentSeasonDisplay');
} else {
  console.log('❌ Seasonal hook file does not exist');
}

// Print the current season
console.log('\nCurrent season information:');
console.log(`Current month: ${new Date().toLocaleString('default', { month: 'long' })}`);
console.log(`Current season: ${getCurrentSeason()}`);

console.log('\nVerification complete!');
