/**
 * Meta Component
 * 
 * This component is used to set metadata for SEO.
 */

interface MetaProps {
  title: string;
  description: string;
  keywords?: string[];
  image?: string;
  schema?: any;
}

export const Meta = ({ title, description, keywords, image, schema }: MetaProps) => {
  // This is a mock implementation since we can't actually modify the document head in this example
  return (
    <>
      {/* This would normally update the document head */}
      {/* For demonstration purposes only */}
      <div style={{ display: 'none' }} data-testid="meta-component">
        <div data-meta="title">{title}</div>
        <div data-meta="description">{description}</div>
        {keywords && <div data-meta="keywords">{keywords.join(', ')}</div>}
        {image && <div data-meta="image">{image}</div>}
        {schema && <div data-meta="schema">{JSON.stringify(schema)}</div>}
      </div>
    </>
  );
};

export default Meta;
