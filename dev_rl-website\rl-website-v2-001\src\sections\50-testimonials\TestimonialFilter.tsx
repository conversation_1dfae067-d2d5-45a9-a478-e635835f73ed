import React from 'react';
import { Star } from 'lucide-react';
import { cn } from '@/lib/utils';

interface TestimonialFilterProps {
  selectedRating: number | null;
  onRatingChange: (rating: number | null) => void;
  counts: { [key: number]: number };
  totalCount: number;
}

export const TestimonialFilter: React.FC<TestimonialFilterProps> = ({
  selectedRating,
  onRatingChange,
  counts,
  totalCount
}) => {
  return (
    <div className="bg-white p-4 rounded-lg shadow mb-6">
      <h3 className="font-medium mb-3">Filter etter vurdering</h3>

      <div className="space-y-2">
        <button
          onClick={() => onRatingChange(null)}
          className={cn(
            'w-full flex items-center justify-between p-2 rounded hover:bg-gray-50 transition-colors',
            selectedRating === null ? 'bg-green-50 text-green-700' : ''
          )}
        >
          <span className="flex items-center">
            <span>Alle vurderinger</span>
          </span>
          <span className="text-gray-500 text-sm">{totalCount}</span>
        </button>

        {[5, 4, 3, 2, 1].map((rating) => (
          <button
            key={rating}
            onClick={() => onRatingChange(rating)}
            className={cn(
              'w-full flex items-center justify-between p-2 rounded hover:bg-gray-50 transition-colors',
              selectedRating === rating ? 'bg-green-50 text-green-700' : ''
            )}
            disabled={!counts[rating]}
          >
            <span className="flex items-center">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className={cn(
                    'w-4 h-4',
                    i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
                  )}
                />
              ))}
            </span>
            <span className="text-gray-500 text-sm">
              {counts[rating] || 0}
            </span>
          </button>
        ))}
      </div>
    </div>
  );
};

export default TestimonialFilter;