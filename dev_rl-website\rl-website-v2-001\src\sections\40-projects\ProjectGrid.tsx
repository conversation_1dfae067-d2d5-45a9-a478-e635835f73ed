import React from 'react';
import { ProjectType } from '@/lib/types';
import { ProjectCard } from './ProjectCard';
import { cn } from '@/lib/utils';
import {
  filterProjects
} from '@/lib/utils/filtering';

interface ProjectGridProps {
  projects: ProjectType[];
  filter?: {
    category?: string;
    location?: string;
    tag?: string;
  };
  layout?: 'grid' | 'masonry' | 'carousel';
  variant?: 'default' | 'featured' | 'compact';
  className?: string;
}

export const ProjectGrid: React.FC<ProjectGridProps> = ({
  projects,
  filter = {},
  layout = 'grid',
  variant = 'default',
  className = ''
}) => {
  // Use the centralized filtering utility
  const filteredProjects = filterProjects(
    projects,
    filter.category || null,
    filter.location || null,
    filter.tag || null
  );

  if (layout === 'masonry') {
    // Simple masonry-like layout with different heights
    return (
      <div className={cn(
        'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6',
        className
      )}>
        {filteredProjects.map((project, index) => (
          <div
            key={project.id}
            className={index % 3 === 0 ? 'row-span-2' : ''}
          >
            <ProjectCard project={project} variant={variant} />
          </div>
        ))}
      </div>
    );
  }

  if (layout === 'carousel') {
    // Simple carousel layout
    return (
      <div className={cn(
        'flex overflow-x-auto gap-6 pb-4',
        className
      )}>
        {filteredProjects.map((project) => (
          <div
            key={project.id}
            className="min-w-[300px] max-w-[400px]"
          >
            <ProjectCard project={project} variant={variant} />
          </div>
        ))}
      </div>
    );
  }

  // Default grid layout
  return (
    <div className={cn(
      'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6',
      className
    )}>
      {filteredProjects.map((project) => (
        <ProjectCard
          key={project.id}
          project={project}
          variant={variant}
        />
      ))}
    </div>
  );
};

export default ProjectGrid;