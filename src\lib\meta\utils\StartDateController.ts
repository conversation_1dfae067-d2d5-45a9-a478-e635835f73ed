/**
 * Start Date Controller - Smart date input logic for employment start date fields
 * Handles arrow key navigation, business constraints, and smart initialization
 */

export interface StartDateConstraints {
  minYear: number;  // 2015 (company founding year)
  maxYear: number;  // currentYear + 10 (planning horizon)
  currentYear: number;
  companyFoundingYear: number;
  planningHorizonYears: number;
}

export class StartDateController {
  private constraints: StartDateConstraints;

  constructor() {
    const currentYear = new Date().getFullYear();
    const companyFoundingYear = 2015;
    const planningHorizonYears = 10;
    
    this.constraints = {
      minYear: companyFoundingYear,  // Company was founded in 2015
      maxYear: currentYear + planningHorizonYears,  // 10 years into future
      currentYear,
      companyFoundingYear,
      planningHorizonYears
    };
  }

  /**
   * Handles arrow key navigation for date fields
   * @param key - The pressed key ('ArrowUp' or 'ArrowDown')
   * @param currentValue - Current input value
   * @param cursorPosition - Current cursor position in input
   * @returns New date string or null if no change needed
   */
  handleArrowKeyNavigation(
    key: string,
    currentValue: string,
    _cursorPosition: number
  ): string | null {
    // Only handle arrow keys
    if (!['ArrowUp', 'ArrowDown'].includes(key)) {
      return null;
    }

    // For HTML5 date inputs, let the browser handle all navigation natively
    // This allows proper day/month/year navigation without interference
    // We only intercept when the field is empty to provide smart initialization

    if (!currentValue || currentValue === '') {
      // Initialize empty field with smart default (today or next Monday)
      const smartDefault = this.getSmartDefaultDate();
      return this.formatDateForInput(smartDefault);
    }

    // For non-empty fields, let browser handle navigation natively
    return null;
  }

  /**
   * Validates and corrects a date input
   * @param inputValue - The input value to validate
   * @returns Corrected date string or original if valid
   */
  validateAndCorrect(inputValue: string): string {
    if (!inputValue) return inputValue;

    const date = this.parseInputValue(inputValue);
    if (!date) return inputValue;

    const year = date.getFullYear();
    
    // Clamp year to valid business range
    const correctedYear = Math.max(
      this.constraints.minYear,
      Math.min(year, this.constraints.maxYear)
    );

    if (correctedYear !== year) {
      const correctedDate = new Date(date);
      correctedDate.setFullYear(correctedYear);
      return this.formatDateForInput(correctedDate);
    }

    return inputValue;
  }

  /**
   * Gets HTML attributes for the input field
   */
  getHTMLAttributes() {
    return {
      min: `${this.constraints.minYear}-01-01`,
      max: `${this.constraints.maxYear}-12-31`,
      placeholder: 'mm/dd/yyyy'
    };
  }

  /**
   * Gets smart default date for start date (today or next Monday)
   */
  private getSmartDefaultDate(): Date {
    const today = new Date();
    const dayOfWeek = today.getDay(); // 0 = Sunday, 1 = Monday, etc.
    
    // If it's Friday (5), Saturday (6), or Sunday (0), suggest next Monday
    if (dayOfWeek === 0 || dayOfWeek === 5 || dayOfWeek === 6) {
      const daysUntilMonday = dayOfWeek === 0 ? 1 : (8 - dayOfWeek);
      const nextMonday = new Date(today);
      nextMonday.setDate(today.getDate() + daysUntilMonday);
      return nextMonday;
    }
    
    // Otherwise, suggest today
    return today;
  }



  /**
   * Parses input value to Date object
   */
  private parseInputValue(value: string): Date | null {
    if (!value) return null;
    
    const date = new Date(value);
    return isNaN(date.getTime()) ? null : date;
  }

  /**
   * Formats date for HTML date input (yyyy-mm-dd)
   */
  private formatDateForInput(date: Date): string {
    return date.toISOString().split('T')[0];
  }

  /**
   * Gets current constraints
   */
  getConstraints(): StartDateConstraints {
    return { ...this.constraints };
  }
}
