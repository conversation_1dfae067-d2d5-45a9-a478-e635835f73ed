/**
 * Formatting utilities for dates, strings, and phone numbers
 */

/**
 * Formats a date into a localized string (Norwegian format)
 */
export const formatDate = (date: string | Date): string => {
    const dateObj = typeof date === "string" ? new Date(date) : date;
    return new Intl.DateTimeFormat("no-NO", {
        month: "long",
        year: "numeric",
    }).format(dateObj);
};


/**
 * Formats a phone number into a readable format (XXX XX XXX)
 */
export const formatPhoneNumber = (phone: string): string => {
    const cleaned = phone.replace(/\D/g, "");
    if (cleaned.length === 8) {
        return cleaned.replace(/(\d{3})(\d{2})(\d{3})/, "$1 $2 $3");
    }
    return phone;
};
