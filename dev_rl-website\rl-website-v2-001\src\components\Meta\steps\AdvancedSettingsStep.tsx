import { Card, Button } from '@/ui';
import { Input, Select, Textarea } from '@/ui/Form';
import { ContractStepProps, paymentDayOptions } from '@/lib/meta/types';
import { ArrowLeft, ArrowRight, Building, Clock, DollarSign, Shield } from 'lucide-react';

const AdvancedSettingsStep = ({ formData, updateFormData, onNext, onPrev }: ContractStepProps) => {
  const handleInputChange = (field: string, value: string | number) => {
    updateFormData({ [field]: value });
  };

  return (
    <div className="space-y-6">
      {/* Company Information */}
      <Card title="Bedriftsinformasjon" className="mb-6">
        <div className="space-y-4">
          <div className="flex items-center mb-4">
            <Building className="h-5 w-5 text-green-600 mr-2" />
            <h3 className="text-base sm:text-lg font-medium text-gray-900">Arbeidsgiverens opplysninger</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Bedriftsnavn"
              value={formData.companyName}
              onChange={(e) => handleInputChange('companyName', e.target.value)}
            />
            
            <Input
              label="Organisasjonsnummer"
              value={formData.companyOrgNumber}
              onChange={(e) => handleInputChange('companyOrgNumber', e.target.value)}
            />
          </div>

          <Input
            label="Bedriftsadresse"
            value={formData.companyAddress}
            onChange={(e) => handleInputChange('companyAddress', e.target.value)}
          />
        </div>
      </Card>

      {/* Working Hours */}
      <Card title="Arbeidstid og pauser" className="mb-6">
        <div className="space-y-4">
          <div className="flex items-center mb-4">
            <Clock className="h-5 w-5 text-green-600 mr-2" />
            <h3 className="text-base sm:text-lg font-medium text-gray-900">Arbeidstidsordning</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Timer per uke"
              type="number"
              step="0.5"
              value={formData.workingHoursPerWeek}
              onChange={(e) => handleInputChange('workingHoursPerWeek', Number(e.target.value))}
            />
            
            <Input
              label="Arbeidstid"
              value={formData.workingTime}
              onChange={(e) => handleInputChange('workingTime', e.target.value)}
              placeholder="07:00-15:00"
            />
          </div>

          <Textarea
            label="Pauseregler"
            value={formData.breakTime}
            onChange={(e) => handleInputChange('breakTime', e.target.value)}
            rows={2}
          />
        </div>
      </Card>

      {/* Compensation */}
      <Card title="Godtgjørelser og utbetaling" className="mb-6">
        <div className="space-y-4">
          <div className="flex items-center mb-4">
            <DollarSign className="h-5 w-5 text-green-600 mr-2" />
            <h3 className="text-base sm:text-lg font-medium text-gray-900">Lønn og tillegg</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Overtidstillegg (%)"
              type="number"
              value={formData.overtimeRate}
              onChange={(e) => handleInputChange('overtimeRate', Number(e.target.value))}
            />
            
            <Select
              label="Utbetalingsdag"
              options={paymentDayOptions}
              value={formData.paymentDay.toString()}
              onChange={(e) => handleInputChange('paymentDay', Number(e.target.value))}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Textarea
              label="Verktøygodtgjørelse"
              value={formData.toolAllowance}
              onChange={(e) => handleInputChange('toolAllowance', e.target.value)}
              rows={2}
            />
            
            <Textarea
              label="Kjøregodtgjørelse"
              value={formData.travelAllowance}
              onChange={(e) => handleInputChange('travelAllowance', e.target.value)}
              rows={2}
            />
          </div>
        </div>
      </Card>

      {/* Pension and Insurance */}
      <Card title="Pensjon og forsikring" className="mb-6">
        <div className="space-y-4">
          <div className="flex items-center mb-4">
            <Shield className="h-5 w-5 text-green-600 mr-2" />
            <h3 className="text-base sm:text-lg font-medium text-gray-900">Pensjon og forsikringsordninger</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Pensjonsleverandør"
              value={formData.pensionProvider}
              onChange={(e) => handleInputChange('pensionProvider', e.target.value)}
            />
            
            <Input
              label="Pensjon org.nr"
              value={formData.pensionOrgNumber}
              onChange={(e) => handleInputChange('pensionOrgNumber', e.target.value)}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Forsikringsleverandør"
              value={formData.insuranceProvider}
              onChange={(e) => handleInputChange('insuranceProvider', e.target.value)}
            />
            
            <Input
              label="Forsikring org.nr"
              value={formData.insuranceOrgNumber}
              onChange={(e) => handleInputChange('insuranceOrgNumber', e.target.value)}
            />
          </div>
        </div>
      </Card>

      {/* Legal Terms */}
      <Card title="Juridiske bestemmelser" className="mb-6">
        <div className="space-y-4">
          <div className="flex items-center mb-4">
            <Shield className="h-5 w-5 text-green-600 mr-2" />
            <h3 className="text-base sm:text-lg font-medium text-gray-900">Oppsigelse og varslingsregler</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Textarea
              label="Oppsigelsesfrister"
              value={formData.noticePeriod}
              onChange={(e) => handleInputChange('noticePeriod', e.target.value)}
              rows={2}
            />
            
            <Textarea
              label="Kontraktvarighet (hvis relevant)"
              value={formData.contractDuration}
              onChange={(e) => handleInputChange('contractDuration', e.target.value)}
              rows={2}
              placeholder="La stå tom for fast ansettelse"
            />
          </div>

          <Textarea
            label="Varslingsregler for endringer"
            value={formData.notificationRules}
            onChange={(e) => handleInputChange('notificationRules', e.target.value)}
            rows={3}
          />
        </div>
      </Card>

      {/* Navigation */}
      <div className="flex justify-between">
        <Button
          onClick={onPrev}
          variant="secondary"
          size="lg"
        >
          <ArrowLeft className="h-5 w-5 mr-2" />
          Forrige steg
        </Button>
        
        <Button
          onClick={onNext}
          variant="primary"
          size="lg"
        >
          Generer kontrakt
          <ArrowRight className="h-5 w-5 ml-2" />
        </Button>
      </div>
    </div>
  );
};

export default AdvancedSettingsStep;
