├── app (1)
│   └── index.tsx
├── content (3)
│   ├── locations (1)
│   │   └── index.ts
│   ├── team (1)
│   │   └── index.ts
│   └── index.ts
├── data (3)
│   ├── projects.ts
│   ├── services.ts
│   └── testimonials.ts
├── docs (2)
│   ├── API_MIGRATION_PLAN.md
│   └── SEO_USAGE.md
├── layout (4)
│   ├── Footer.tsx
│   ├── Header.tsx
│   ├── Meta.tsx
│   └── index.ts
├── lib (9)
│   ├── api (4)
│   │   ├── README.md
│   │   ├── enhanced.ts
│   │   ├── index.ts
│   │   └── sync.ts
│   ├── config (4)
│   │   ├── images.ts
│   │   ├── index.ts
│   │   ├── paths.ts
│   │   └── site.ts
│   ├── constants (3)
│   │   ├── data.ts
│   │   ├── index.ts
│   │   └── site.ts
│   ├── context (1)
│   │   └── AppContext.tsx
│   ├── hooks (5)
│   │   ├── index.ts
│   │   ├── useAnalytics.ts
│   │   ├── useData.ts
│   │   ├── useEventListener.ts
│   │   └── useMediaQuery.ts
│   ├── types (3)
│   │   ├── components.ts
│   │   ├── content.ts
│   │   └── index.ts
│   ├── utils (11)
│   │   ├── analytics.ts
│   │   ├── debug.ts
│   │   ├── dom.ts
│   │   ├── formatting.ts
│   │   ├── images.ts
│   │   ├── index.ts
│   │   ├── paths.ts
│   │   ├── seasonal.ts
│   │   ├── seo.ts
│   │   ├── strings.ts
│   │   └── validation.ts
│   ├── README.md
│   └── config.ts
├── sections (6)
│   ├── 10-home (3)
│   │   ├── FilteredServicesSection.tsx
│   │   ├── SeasonalProjectsCarousel.tsx
│   │   └── index.tsx
│   ├── 20-about (1)
│   │   └── index.tsx
│   ├── 30-services (3)
│   │   ├── components (1)
│   │   │   └── ServiceCard.tsx
│   │   ├── detail.tsx
│   │   └── index.tsx
│   ├── 40-projects (7)
│   │   ├── ProjectCard.tsx
│   │   ├── ProjectFilter.tsx
│   │   ├── ProjectGallery.tsx
│   │   ├── ProjectGrid.tsx
│   │   ├── ProjectsCarousel.tsx
│   │   ├── detail.tsx
│   │   └── index.tsx
│   ├── 50-testimonials (7)
│   │   ├── AverageRating.tsx
│   │   ├── Testimonial.tsx
│   │   ├── TestimonialFilter.tsx
│   │   ├── TestimonialSlider.tsx
│   │   ├── TestimonialsPage.tsx
│   │   ├── TestimonialsSchema.tsx
│   │   └── index.ts
│   └── 60-contact (1)
│       └── index.tsx
├── styles (3)
│   ├── animations.css
│   ├── base.css
│   └── utilities.css
├── ui (18)
│   ├── Form (4)
│   │   ├── Input.tsx
│   │   ├── Select.tsx
│   │   ├── Textarea.tsx
│   │   └── index.ts
│   ├── Button.tsx
│   ├── Card.tsx
│   ├── Container.tsx
│   ├── ContentGrid.tsx
│   ├── Hero.tsx
│   ├── Icon.tsx
│   ├── Intersection.tsx
│   ├── Loading.tsx
│   ├── Logo.tsx
│   ├── Notifications.tsx
│   ├── PageSection.tsx
│   ├── SeasonalCTA.tsx
│   ├── SectionHeading.tsx
│   ├── ServiceAreaList.tsx
│   ├── Skeleton.tsx
│   ├── Transition.tsx
│   └── index.ts
├── index.css
├── main.tsx
└── vite-env.d.ts