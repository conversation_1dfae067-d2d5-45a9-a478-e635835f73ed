/**
 * Contact Information Consolidation Verification Script
 * 
 * This script verifies that contact information has been properly consolidated
 * by checking for any remaining hardcoded contact information in the codebase.
 */

const fs = require('fs');
const path = require('path');

// Contact information patterns to search for
const PATTERNS = {
  // Norwegian phone number formats
  phoneNumbers: [
    /\+47\s*\d{2}\s*\d{2}\s*\d{2}\s*\d{2}/g,  // +47 12 34 56 78
    /\+47\s*\d{3}\s*\d{2}\s*\d{3}/g,          // +47 123 45 678
    /\+47\d{8}/g,                             // +4712345678
    /\(\+47\)\s*\d{8}/g,                      // (+47) 12345678
  ],
  
  // Email patterns (specific to the company)
  emails: [
    /@ringerikelandskap\.no/g
  ]
};

// Files to exclude (including our contact constants file and verification script)
const EXCLUDE_FILES = [
  'src/lib/constants/contact.ts',
  'src/lib/constants/site.ts',
  'scripts/verify-contact.cjs'
];

// File extensions to scan
const EXTENSIONS_TO_SCAN = ['.tsx', '.ts', '.jsx', '.js'];

// Directories to exclude
const EXCLUDE_DIRS = ['node_modules', 'dist', '.git', 'scripts'];

// Results storage
const results = {
  totalOccurrences: 0,
  fileOccurrences: {},
  patternCounts: {
    phoneNumbers: 0,
    emails: 0
  }
};

/**
 * Scans a file for hardcoded contact information
 */
function scanFile(filePath) {
  // Skip excluded files
  if (EXCLUDE_FILES.some(excludePath => filePath.includes(excludePath))) {
    return;
  }

  try {
    const content = fs.readFileSync(filePath, 'utf8');
    let fileHasOccurrences = false;
    const fileOccurrences = [];

    // Skip files that import from the contact constants
    if (content.includes("from '@/lib/constants/contact'") || 
        content.includes("from \"@/lib/constants/contact\"")) {
      // This file is using the consolidated contact info, so we'll skip it
      return;
    }

    // Check each pattern type
    for (const [patternType, patterns] of Object.entries(PATTERNS)) {
      patterns.forEach(pattern => {
        // Reset the pattern's lastIndex to ensure we start from the beginning
        if (pattern.global) pattern.lastIndex = 0;
        
        const matches = content.match(pattern);
        
        if (matches && matches.length > 0) {
          fileHasOccurrences = true;
          results.patternCounts[patternType] += matches.length;
          results.totalOccurrences += matches.length;
          
          // Find line numbers for each occurrence
          const lines = content.split('\n');
          const occurrences = [];
          
          lines.forEach((line, index) => {
            // Reset the pattern's lastIndex for each line
            if (pattern.global) pattern.lastIndex = 0;
            
            if (pattern.test(line)) {
              occurrences.push({
                line: index + 1,
                content: line.trim()
              });
            }
          });
          
          fileOccurrences.push({
            patternType,
            pattern: pattern.toString(),
            count: matches.length,
            occurrences
          });
        }
      });
    }
    
    if (fileHasOccurrences) {
      results.fileOccurrences[filePath] = fileOccurrences;
    }
  } catch (error) {
    console.error(`Error scanning file ${filePath}:`, error.message);
  }
}

/**
 * Recursively scans a directory for files to analyze
 */
function scanDirectory(dirPath) {
  try {
    const entries = fs.readdirSync(dirPath, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = path.join(dirPath, entry.name);
      
      if (entry.isDirectory()) {
        // Skip excluded directories
        if (!EXCLUDE_DIRS.includes(entry.name)) {
          scanDirectory(fullPath);
        }
      } else if (entry.isFile()) {
        // Only scan files with specified extensions
        const ext = path.extname(entry.name).toLowerCase();
        if (EXTENSIONS_TO_SCAN.includes(ext)) {
          scanFile(fullPath);
        }
      }
    }
  } catch (error) {
    console.error(`Error scanning directory ${dirPath}:`, error.message);
  }
}

// Start the scan from the src directory
console.log('Starting contact information verification...');
const startTime = Date.now();

// Get the root directory
const rootDir = path.resolve(__dirname, '../src');
scanDirectory(rootDir);

// Generate the report
const endTime = Date.now();
const scanDuration = ((endTime - startTime) / 1000).toFixed(2);

console.log(`\nContact Information Verification Complete (${scanDuration}s)`);
console.log(`Total unconsolidated occurrences found: ${results.totalOccurrences}`);

if (results.totalOccurrences > 0) {
  console.log('\nPattern Counts:');
  Object.entries(results.patternCounts).forEach(([pattern, count]) => {
    if (count > 0) {
      console.log(`  ${pattern}: ${count}`);
    }
  });

  console.log('\nFiles with Unconsolidated Contact Information:');
  Object.keys(results.fileOccurrences).forEach(filePath => {
    const relPath = path.relative(rootDir, filePath);
    const totalInFile = results.fileOccurrences[filePath].reduce((sum, item) => sum + item.count, 0);
    console.log(`  ${relPath} (${totalInFile} occurrences)`);
  });

  // Write detailed results to a JSON file
  const outputPath = path.join(__dirname, 'contact-verification-report.json');
  fs.writeFileSync(outputPath, JSON.stringify(results, null, 2));
  console.log(`\nDetailed report written to: ${outputPath}`);

  // Exit with error code to indicate unconsolidated references
  process.exit(1);
} else {
  console.log('\nSuccess! All contact information has been properly consolidated.');
  process.exit(0);
}
