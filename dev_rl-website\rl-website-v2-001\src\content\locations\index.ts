import { ServiceArea } from '@/lib/types';
// Import removed to fix TS6133 error
/**
 * @deprecated This file is deprecated and will be removed in a future release.
 * Please use the API layer in src/lib/api/index.ts instead.
 */

/**
 * Service areas where Ringerike Landskap operates
 */
export const serviceAreas: ServiceArea[] = [
  {
    city: 'Røyse',
    distance: '0 km',
    description: 'Vårt hovedkontor ligger på Røyse i Hole kommune.',
    isBase: true
  },
  {
    city: 'Hole',
    distance: '5 km',
    description: 'Vi dekker hele Hole kommune med alle våre tjenester.'
  },
  {
    city: 'Hønefoss',
    distance: '15 km',
    description: 'Vi utfører oppdrag i hele Hønefoss og omegn.'
  },
  {
    city: 'Ringerike',
    distance: '20 km',
    description: 'Vi dekker hele Ringerike kommune med alle våre tjenester.'
  },
  {
    city: 'Jevnaker',
    distance: '25 km',
    description: 'Vi tar oppdrag i Jevnaker kommune.'
  },
  {
    city: 'Sundvollen',
    distance: '10 km',
    description: 'Vi utfører alle typer anleggsgartnertjenester i Sundvollen.'
  }
];

/**
 * Get all service areas
 * @returns Array of service areas
 * @deprecated Use getServiceAreas from @/lib/api instead
 */
export function getServiceAreas(): ServiceArea[] {
  console.warn('Deprecated: Use getServiceAreas from @/lib/api instead');
  // This is a synchronous wrapper around the async API
  // It will return the static data for backward compatibility
  return serviceAreas;
}

/**
 * Get service area by city name
 * @param city City name
 * @returns Service area or undefined if not found
 * @deprecated Use the API layer in @/lib/api instead
 */
export function getServiceAreaByCity(city: string): ServiceArea | undefined {
  console.warn('Deprecated: Use the API layer in @/lib/api instead');
  return serviceAreas.find(area => area.city.toLowerCase() === city.toLowerCase());
}

/**
 * Get the base service area (where the company is located)
 * @returns Base service area
 * @deprecated Use the API layer in @/lib/api instead
 */
export function getBaseServiceArea(): ServiceArea | undefined {
  console.warn('Deprecated: Use the API layer in @/lib/api instead');
  return serviceAreas.find(area => area.isBase);
}

/**
 * Get service areas within a specific distance
 * @param maxDistance Maximum distance in km
 * @returns Array of service areas within the specified distance
 */
export function getServiceAreasWithinDistance(maxDistance: number): ServiceArea[] {
  return serviceAreas.filter(area => {
    const distance = parseInt(area.distance.split(' ')[0]);
    return distance <= maxDistance;
  });
}
