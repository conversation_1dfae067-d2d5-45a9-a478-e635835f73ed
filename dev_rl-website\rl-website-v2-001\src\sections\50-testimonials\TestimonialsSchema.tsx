import React from "react";
import { TestimonialType } from "@/lib/types";

interface TestimonialsSchemaProps {
    testimonials: TestimonialType[];
    companyName?: string;
    companyUrl?: string;
}

const TestimonialsSchema: React.FC<TestimonialsSchemaProps> = ({
    testimonials,
    companyName = "Ringerike Landskap",
    companyUrl = "https://ringerikelandskap.no",
}) => {
    // Calculate average rating
    const totalRating = testimonials.reduce(
        (sum, testimonial) => sum + testimonial.rating,
        0
    );
    const averageRating = totalRating / testimonials.length;

    // Create schema.org JSON-LD data
    const schemaData = {
        "@context": "https://schema.org",
        "@type": "LocalBusiness",
        name: companyName,
        url: companyUrl,
        aggregateRating: {
            "@type": "AggregateRating",
            ratingValue: averageRating.toFixed(1),
            reviewCount: testimonials.length,
            bestRating: "5",
            worstRating: "1",
        },
        review: testimonials.map((testimonial) => ({
            "@type": "Review",
            author: {
                "@type": "Person",
                name: testimonial.name,
            },
            reviewRating: {
                "@type": "Rating",
                ratingValue: testimonial.rating,
                bestRating: "5",
                worstRating: "1",
            },
            reviewBody: testimonial.text,
            datePublished: testimonial.date || new Date().toISOString().split("T")[0], // Use testimonial date if available
            ...(testimonial.sourceUrl ? { url: testimonial.sourceUrl } : {})
        })),
    };

    return (
        <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{ __html: JSON.stringify(schemaData) }}
        />
    );
};

export default TestimonialsSchema;
