import React from 'react';
import { cn  } from '@/lib/utils';

type GridColumns = 1 | 2 | 3 | 4;
type GridGap = 'small' | 'medium' | 'large';

interface ContentGridProps {
  children: React.ReactNode;
  columns?: GridColumns;
  mdColumns?: GridColumns;
  lgColumns?: GridColumns;
  gap?: GridGap;
  className?: string;
}

/**
 * ContentGrid component for consistent grid layouts across the site
 * 
 * This component provides standardized grid layouts with responsive column
 * configurations and consistent spacing.
 */
const ContentGrid: React.FC<ContentGridProps> = ({
  children,
  columns = 1,
  mdColumns,
  lgColumns,
  gap = 'medium',
  className
}) => {
  // Base grid classes
  const gridClasses = `grid`;

  // Column classes for different breakpoints
  const columnClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-2',
    3: 'grid-cols-3',
    4: 'grid-cols-4'
  };

  // Medium breakpoint column classes
  const mdColumnClasses = mdColumns ? {
    1: 'md:grid-cols-1',
    2: 'md:grid-cols-2',
    3: 'md:grid-cols-3',
    4: 'md:grid-cols-4'
  } : {};

  // Large breakpoint column classes
  const lgColumnClasses = lgColumns ? {
    1: 'lg:grid-cols-1',
    2: 'lg:grid-cols-2',
    3: 'lg:grid-cols-3',
    4: 'lg:grid-cols-4'
  } : {};

  // Gap classes
  const gapClasses = {
    small: 'gap-4',
    medium: 'gap-6 sm:gap-8',
    large: 'gap-8 sm:gap-10'
  };

  return (
    <div 
      className={cn(
        gridClasses,
        columnClasses[columns],
        mdColumns && mdColumnClasses[mdColumns],
        lgColumns && lgColumnClasses[lgColumns],
        gapClasses[gap],
        className
      )}
    >
      {children}
    </div>
  );
};

export { ContentGrid };
export default ContentGrid;