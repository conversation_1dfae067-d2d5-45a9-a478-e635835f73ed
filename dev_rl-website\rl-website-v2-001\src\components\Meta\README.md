# Meta Components

This directory contains components for the Meta Utilities system - internal tools for Ringerike Landskap AS.

## Architecture

The Meta components are **architecturally isolated** from the main website to ensure stability and prevent coupling.

## Components

### Core Components
- **`MetaRouter.tsx`** - Isolated router for all meta utilities with lazy loading
- **`MetaErrorBoundary.tsx`** - Error boundary to prevent meta utility failures from affecting main site

### Utility Components
- **`ArbeidskontraktGenerator.tsx`** - Employment contract generator
- **`steps/`** - Multi-step wizard components for contract generation

## Design Principles

1. **Isolation** - Meta utilities cannot affect main website functionality
2. **Error Boundaries** - Graceful degradation when utilities fail
3. **Lazy Loading** - Components loaded only when needed
4. **Type Safety** - Uses isolated type system from `@/lib/meta/types`

## Usage

Meta components are accessed through the isolated router:
```typescript
// In main app router
<Route path="/meta/*" element={<MetaRouter />} />
```

Individual utilities are wrapped in error boundaries:
```typescript
<MetaErrorBoundary fallbackTitle="Tool Error">
  <UtilityComponent />
</MetaErrorBoundary>
```

## Adding New Meta Utilities

1. Create component in this directory
2. Add types to `@/lib/meta/types`
3. Add route to `MetaRouter.tsx`
4. Wrap in `MetaErrorBoundary`
5. Test error scenarios

## Import Patterns

Follow established codebase conventions:
```typescript
// External dependencies
import { useState } from 'react';

// UI components (barrel imports)
import { Card, Button } from '@/ui';

// Form components
import { Input, Select } from '@/ui/Form';

// Meta types (isolated)
import { ContractFormData } from '@/lib/meta/types';

// Local components (relative)
import LocalComponent from './LocalComponent';
```
