/**
 * Location Consolidation Verification Script
 * 
 * This script verifies that location references have been properly consolidated
 * by checking for any remaining hardcoded location references in the codebase.
 */

const fs = require('fs');
const path = require('path');

// Location keywords to search for
const LOCATION_KEYWORDS = [
  'Ringerike',
  'ringerike',
  'Ringerikes',
  'ringerikes',
  'ringeriksregionen',
  'Rø<PERSON>e',
  'rø<PERSON>e',
  'Hole kommune',
  'hole kommune',
  'Hole',
  'Hønefoss',
  'hønefoss',
  'Tyrifjorden',
  'tyrifjorden'
];

// Files to exclude (including our locations constants file)
const EXCLUDE_FILES = [
  'src/lib/constants/locations.ts',
  'scripts/verify-locations.cjs'
];

// File extensions to scan
const EXTENSIONS_TO_SCAN = ['.tsx', '.ts', '.jsx', '.js'];

// Directories to exclude
const EXCLUDE_DIRS = ['node_modules', 'dist', '.git', 'scripts'];

// Results storage
const results = {
  totalOccurrences: 0,
  fileOccurrences: {},
  keywordCounts: {}
};

// Initialize keyword counts
LOCATION_KEYWORDS.forEach(keyword => {
  results.keywordCounts[keyword] = 0;
});

/**
 * Scans a file for location references
 */
function scanFile(filePath) {
  // Skip excluded files
  if (EXCLUDE_FILES.some(excludePath => filePath.includes(excludePath))) {
    return;
  }

  try {
    const content = fs.readFileSync(filePath, 'utf8');
    let fileHasOccurrences = false;
    const fileOccurrences = [];

    // Skip files that import from the locations constants
    if (content.includes("from '@/lib/constants/locations'") || 
        content.includes("from '@/lib/constants'") ||
        content.includes("from \"@/lib/constants/locations\"") ||
        content.includes("from \"@/lib/constants\"")) {
      // This file is using the consolidated locations, so we'll skip it
      return;
    }

    LOCATION_KEYWORDS.forEach(keyword => {
      // Use regex to find all occurrences
      const regex = new RegExp(`\\b${keyword}\\b`, 'g');
      const matches = content.match(regex);
      
      if (matches && matches.length > 0) {
        fileHasOccurrences = true;
        results.keywordCounts[keyword] += matches.length;
        results.totalOccurrences += matches.length;
        
        // Find line numbers for each occurrence
        const lines = content.split('\n');
        const occurrences = [];
        
        lines.forEach((line, index) => {
          if (line.match(regex)) {
            occurrences.push({
              line: index + 1,
              content: line.trim()
            });
          }
        });
        
        fileOccurrences.push({
          keyword,
          count: matches.length,
          occurrences
        });
      }
    });
    
    if (fileHasOccurrences) {
      results.fileOccurrences[filePath] = fileOccurrences;
    }
  } catch (error) {
    console.error(`Error scanning file ${filePath}:`, error.message);
  }
}

/**
 * Recursively scans a directory for files to analyze
 */
function scanDirectory(dirPath) {
  try {
    const entries = fs.readdirSync(dirPath, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = path.join(dirPath, entry.name);
      
      if (entry.isDirectory()) {
        // Skip excluded directories
        if (!EXCLUDE_DIRS.includes(entry.name)) {
          scanDirectory(fullPath);
        }
      } else if (entry.isFile()) {
        // Only scan files with specified extensions
        const ext = path.extname(entry.name).toLowerCase();
        if (EXTENSIONS_TO_SCAN.includes(ext)) {
          scanFile(fullPath);
        }
      }
    }
  } catch (error) {
    console.error(`Error scanning directory ${dirPath}:`, error.message);
  }
}

// Start the scan from the src directory
console.log('Starting location references verification...');
const startTime = Date.now();

// Get the root directory
const rootDir = path.resolve(__dirname, '../src');
scanDirectory(rootDir);

// Generate the report
const endTime = Date.now();
const scanDuration = ((endTime - startTime) / 1000).toFixed(2);

console.log(`\nLocation References Verification Complete (${scanDuration}s)`);
console.log(`Total unconsolidated occurrences found: ${results.totalOccurrences}`);

if (results.totalOccurrences > 0) {
  console.log('\nKeyword Counts:');
  Object.entries(results.keywordCounts)
    .sort((a, b) => b[1] - a[1])
    .forEach(([keyword, count]) => {
      if (count > 0) {
        console.log(`  ${keyword}: ${count}`);
      }
    });

  console.log('\nFiles with Unconsolidated Location References:');
  Object.keys(results.fileOccurrences).forEach(filePath => {
    const relPath = path.relative(rootDir, filePath);
    const totalInFile = results.fileOccurrences[filePath].reduce((sum, item) => sum + item.count, 0);
    console.log(`  ${relPath} (${totalInFile} occurrences)`);
  });

  // Write detailed results to a JSON file
  const outputPath = path.join(__dirname, 'location-verification-report.json');
  fs.writeFileSync(outputPath, JSON.stringify(results, null, 2));
  console.log(`\nDetailed report written to: ${outputPath}`);

  // Exit with error code to indicate unconsolidated references
  process.exit(1);
} else {
  console.log('\nSuccess! All location references have been properly consolidated.');
  process.exit(0);
}
