
import React from "react";
import { <PERSON> } from "react-router-dom";
import <PERSON> from "@/ui/Hero";
import { ServiceAreaList, SeasonalCTA, PageSection } from "@/ui";
import SeasonalProjectsCarousel from "./SeasonalProjectsCarousel";
import FilteredServicesSection from "./FilteredServicesSection";
import { TestimonialsSection } from "../50-testimonials";
import { useData, useSeasonalData } from "@/lib/hooks";
import { getServiceAreas, getTestimonials } from "@/lib/api";
import { Meta } from "@/layout/Meta";
import { logSeasonalAccess } from "@/lib/utils/debug";
import {
    PRIMARY_AREA,
    COMPANY_BASE,
    getLocationHeroSubtitle
} from "@/lib/constants/locations";

const HomePage = () => {
    // Use our new seasonal hook to get seasonal data
    const { currentSeason } = useSeasonalData();

    // Log seasonal access for debugging
    React.useEffect(() => {
        logSeasonalAccess('HomePage', currentSeason, {
            component: 'HomePage'
        });
    }, [currentSeason]);

    const { data: serviceAreas, loading: areasLoading } = useData(
        getServiceAreas,
        []
    );
    const { data: testimonials, loading: testimonialsLoading } = useData(
        getTestimonials,
        []
    );

    return (
        <>
            <Meta
                title="Hjem"
                description={PRIMARY_AREA.seoDescription}
                image="/images/hero/hero-home-main.webp"
                keywords={[
                    "anleggsgartner",
                    "maskinentreprenør",
                    PRIMARY_AREA.name.toLowerCase(),
                    "uterom",
                    "hagedesign",
                    "belegningsstein",
                ]}
            />
            <div itemScope itemType="http://schema.org/LandscapingBusiness">
                <Hero
                    title="Anleggsgartner & maskinentreprenør"
                    subtitle={getLocationHeroSubtitle()}
                    location={COMPANY_BASE.fullAddress}
                    yearEstablished="2015"
                    actionLink="/prosjekter"
                    actionText="Se våre prosjekter"
                    backgroundImage="/images/hero/hero-home-main.webp"
                />

                {/* Main Content Section - Combined Layout */}
                <PageSection background="white" spacing="medium">
                    <div className="grid grid-cols-1 lg:grid-cols-12 gap-6 sm:gap-8">
                        {/* Left Column - Projects and Services */}
                        <div className="lg:col-span-8 space-y-8">
                            {/* Projects Carousel */}
                            <div>
                                <div className="flex justify-between items-center mb-4">
                                    <h2 className="text-xl sm:text-2xl font-semibold">
                                        Aktuelle{" "}
                                        <span className="text-green-500">
                                            prosjekter
                                        </span>
                                    </h2>
                                    <Link
                                        to="/prosjekter"
                                        className="text-green-600 hover:text-green-700 font-medium flex items-center gap-1 transition-colors"
                                    >
                                        Se våre prosjekter
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            className="h-4 w-4"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            stroke="currentColor"
                                        >
                                            <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth={2}
                                                d="M9 5l7 7-7 7"
                                            />
                                        </svg>
                                    </Link>
                                </div>
                                <SeasonalProjectsCarousel />
                            </div>

                            {/* Filtered Services Section */}
                            <div>
                                <FilteredServicesSection />
                            </div>
                        </div>

                        {/* Right Column - Service Areas and Seasonal CTA */}
                        <div className="lg:col-span-4 space-y-6">
                            {/* Service Areas */}
                            {areasLoading ? (
                                <div className="h-full bg-gray-200 animate-pulse rounded-lg"></div>
                            ) : (
                                <ServiceAreaList areas={serviceAreas || []} />
                            )}

                            {/* Seasonal CTA */}
                            <SeasonalCTA />
                        </div>
                    </div>
                </PageSection>



                {/* Testimonials Section */}
                <PageSection background="light" spacing="large">
                    {testimonialsLoading ? (
                        <div className="h-64 bg-gray-200 animate-pulse rounded-lg"></div>
                    ) : (
                        <TestimonialsSection testimonials={testimonials || []} />
                    )}
                </PageSection>
            </div>
        </>
    );
};

export default HomePage;
