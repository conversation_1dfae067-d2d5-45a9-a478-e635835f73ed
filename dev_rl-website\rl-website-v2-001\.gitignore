#-----------------------------------------------------------------------------
# Node / Package Managers
#-----------------------------------------------------------------------------
# Dependencies and lock files (if not committing package-lock.json, which is generally NOT recommended)
node_modules/
# yarn classic's lock file (if using npm and package-lock.json)
# yarn.lock
# pnpm's lock file (if using npm and package-lock.json)
# pnpm-lock.yaml

# Log files
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

#-----------------------------------------------------------------------------
# Build Output & Cache
#-----------------------------------------------------------------------------
# Standard build output directory (contains generated assets, HTML, etc.)
dist/

# Vite internal cache directory
.vite/

# Other potential build/cache directories from related tools (e.g., Nitro)
.output/
.nitro/

#-----------------------------------------------------------------------------
# Environment Variables
#-----------------------------------------------------------------------------
# Ignore environment-specific .env files, keep the template/example
config/env/.env.development
config/env/.env.production
config/env/.env.staging

# Alternatively, use a wildcard pattern but explicitly keep the example:
# config/env/.env.*
# !config/env/.env.example

# Ignore any root-level .env files (if any exist now or later)
.env
.env.*
!.env.example
!.env.ci

#-----------------------------------------------------------------------------
# Operating System Files
#-----------------------------------------------------------------------------
# macOS
.DS_Store
.AppleDouble
.LSOverride

# Thumbnails cache for Windows
Thumbs.db
ehthumbs.db
ehthumbs_vista.db

#-----------------------------------------------------------------------------
# IDE / Editor Specific Files
#-----------------------------------------------------------------------------
# VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# JetBrains (WebStorm, IntelliJ IDEA, etc.)
.idea/

# Sublime Text
*.sublime-project
*.sublime-workspace

# Other common editor/temp files
*.swp
*~
*.bak
*.tmp

#-----------------------------------------------------------------------------
# Optional: Test Coverage Reports
#-----------------------------------------------------------------------------
# coverage/
# .nyc_output/

#-----------------------------------------------------------------------------
# Optional: Linting / Formatting Cache
#-----------------------------------------------------------------------------
# .eslintcache
# .prettiercache