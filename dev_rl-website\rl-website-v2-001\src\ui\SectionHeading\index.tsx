import React from 'react';
import { cn  } from '@/lib/utils';

interface SectionHeadingProps {
  title: string;
  subtitle?: string;
  highlightedWord?: string;
  align?: 'left' | 'center' | 'right';
  size?: 'small' | 'medium' | 'large';
  className?: string;
  subtitleClassName?: string;
}

/**
 * SectionHeading component for consistent section headings across the site
 * 
 * This component provides standardized heading styles with optional subtitle
 * and highlighted word for visual emphasis.
 */
const SectionHeading: React.FC<SectionHeadingProps> = ({
  title,
  subtitle,
  highlightedWord,
  align = 'left',
  size = 'medium',
  className,
  subtitleClassName
}) => {
  // Alignment classes
  const alignClasses = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right'
  };

  // Size classes for the heading
  const sizeClasses = {
    small: 'text-xl sm:text-2xl',
    medium: 'text-2xl sm:text-3xl',
    large: 'text-3xl sm:text-4xl md:text-5xl'
  };

  // Spacing below the heading
  const marginClasses = subtitle ? 'mb-2' : 'mb-6 sm:mb-8';

  // Process the title to highlight a specific word if provided
  const renderTitle = () => {
    if (!highlightedWord || !title.includes(highlightedWord)) {
      return title;
    }

    const parts = title.split(highlightedWord);
    return (
      <>
        {parts[0]}
        <span className="text-green-500">{highlightedWord}</span>
        {parts[1]}
      </>
    );
  };

  return (
    <div className={cn(alignClasses[align], 'mb-6 sm:mb-8', className)}>
      <h2 className={cn(sizeClasses[size], 'font-semibold', marginClasses)}>
        {renderTitle()}
      </h2>
      
      {subtitle && (
        <p className={cn('text-gray-600', subtitleClassName)}>
          {subtitle}
        </p>
      )}
    </div>
  );
};

export { SectionHeading };
export default SectionHeading;