/**
 * Formatting utilities for dates, strings, and phone numbers
 */

/**
 * Formats a date into a localized string (Norwegian format)
 */
export const formatDate = (date: string | Date): string => {
    const dateObj = typeof date === "string" ? new Date(date) : date;
    return new Intl.DateTimeFormat("no-NO", {
        month: "long",
        year: "numeric",
    }).format(dateObj);
};

/**
 * Formats a date into DD.MM.YYYY format for contracts
 */
export const formatContractDate = (date: string | Date): string => {
    if (!date) return '__.__.__';
    const dateObj = typeof date === "string" ? new Date(date) : date;

    // Check if date is valid
    if (isNaN(dateObj.getTime())) return '__.__.__';

    return new Intl.DateTimeFormat("no-NO", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric",
    }).format(dateObj);
};


/**
 * Formats a phone number into a readable format (XXX XX XXX)
 */
export const formatPhoneNumber = (phone: string): string => {
    const cleaned = phone.replace(/\D/g, "");
    if (cleaned.length === 8) {
        return cleaned.replace(/(\d{3})(\d{2})(\d{3})/, "$1 $2 $3");
    }
    return phone;
};
